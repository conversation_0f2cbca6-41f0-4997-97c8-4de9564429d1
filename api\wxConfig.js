/**
 * 微信JS-SDK配置API示例
 * 这个文件展示了如何在后端生成微信JS-SDK所需的配置信息
 * 
 * 注意：这只是示例代码，实际使用时需要根据您的后端技术栈进行调整
 */

// 微信公众号配置信息（需要从微信公众平台获取）
const WX_CONFIG = {
  appId: 'your_app_id', // 微信公众号的AppID
  appSecret: 'your_app_secret', // 微信公众号的AppSecret
  token: 'your_token' // 微信公众号的Token
}

/**
 * 获取微信Access Token
 * 注意：实际项目中应该缓存access_token，避免频繁请求
 */
async function getAccessToken() {
  const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WX_CONFIG.appId}&secret=${WX_CONFIG.appSecret}`
  
  try {
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.access_token) {
      return data.access_token
    } else {
      throw new Error(`获取access_token失败: ${data.errmsg}`)
    }
  } catch (error) {
    console.error('获取access_token失败:', error)
    throw error
  }
}

/**
 * 获取微信JS-SDK Ticket
 * 注意：实际项目中应该缓存jsapi_ticket，避免频繁请求
 */
async function getJSApiTicket(accessToken) {
  const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`
  
  try {
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.ticket) {
      return data.ticket
    } else {
      throw new Error(`获取jsapi_ticket失败: ${data.errmsg}`)
    }
  } catch (error) {
    console.error('获取jsapi_ticket失败:', error)
    throw error
  }
}

/**
 * 生成随机字符串
 */
function generateNonceStr(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成时间戳
 */
function generateTimestamp() {
  return Math.floor(Date.now() / 1000).toString()
}

/**
 * 生成签名
 */
function generateSignature(ticket, nonceStr, timestamp, url) {
  const crypto = require('crypto')
  
  // 参数排序并拼接
  const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  
  // SHA1加密
  const signature = crypto.createHash('sha1').update(string1).digest('hex')
  
  return signature
}

/**
 * 获取微信JS-SDK配置
 * 这是主要的API接口，前端会调用这个接口获取配置信息
 */
async function getWxJSSDKConfig(url) {
  try {
    // 1. 获取access_token
    const accessToken = await getAccessToken()
    
    // 2. 获取jsapi_ticket
    const ticket = await getJSApiTicket(accessToken)
    
    // 3. 生成配置参数
    const nonceStr = generateNonceStr()
    const timestamp = generateTimestamp()
    const signature = generateSignature(ticket, nonceStr, timestamp, url)
    
    // 4. 返回配置信息
    return {
      appId: WX_CONFIG.appId,
      timestamp: timestamp,
      nonceStr: nonceStr,
      signature: signature
    }
  } catch (error) {
    console.error('生成微信JS-SDK配置失败:', error)
    throw error
  }
}

// Express.js 路由示例
// app.post('/api/wx/jsapi-config', async (req, res) => {
//   try {
//     const { url } = req.body
//     
//     if (!url) {
//       return res.status(400).json({
//         code: 400,
//         message: '缺少url参数'
//       })
//     }
//     
//     const config = await getWxJSSDKConfig(url)
//     
//     res.json({
//       code: 200,
//       message: '获取成功',
//       data: config
//     })
//   } catch (error) {
//     console.error('获取微信配置失败:', error)
//     res.status(500).json({
//       code: 500,
//       message: '服务器内部错误'
//     })
//   }
// })

// Koa.js 路由示例
// router.post('/api/wx/jsapi-config', async (ctx) => {
//   try {
//     const { url } = ctx.request.body
//     
//     if (!url) {
//       ctx.status = 400
//       ctx.body = {
//         code: 400,
//         message: '缺少url参数'
//       }
//       return
//     }
//     
//     const config = await getWxJSSDKConfig(url)
//     
//     ctx.body = {
//       code: 200,
//       message: '获取成功',
//       data: config
//     }
//   } catch (error) {
//     console.error('获取微信配置失败:', error)
//     ctx.status = 500
//     ctx.body = {
//       code: 500,
//       message: '服务器内部错误'
//     }
//   }
// })

// Spring Boot Controller 示例 (Java)
// @RestController
// @RequestMapping("/api/wx")
// public class WxController {
//     
//     @PostMapping("/jsapi-config")
//     public ResponseEntity<?> getJSApiConfig(@RequestBody Map<String, String> request) {
//         try {
//             String url = request.get("url");
//             if (url == null || url.isEmpty()) {
//                 return ResponseEntity.badRequest().body(Map.of(
//                     "code", 400,
//                     "message", "缺少url参数"
//                 ));
//             }
//             
//             Map<String, String> config = getWxJSSDKConfig(url);
//             
//             return ResponseEntity.ok(Map.of(
//                 "code", 200,
//                 "message", "获取成功",
//                 "data", config
//             ));
//         } catch (Exception e) {
//             logger.error("获取微信配置失败", e);
//             return ResponseEntity.status(500).body(Map.of(
//                 "code", 500,
//                 "message", "服务器内部错误"
//             ));
//         }
//     }
// }

module.exports = {
  getWxJSSDKConfig,
  getAccessToken,
  getJSApiTicket,
  generateNonceStr,
  generateTimestamp,
  generateSignature
}
