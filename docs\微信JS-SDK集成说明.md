# 微信JS-SDK集成说明

本文档说明如何在uni-app H5项目中集成微信JS-SDK，实现微信扫一扫等功能。

## 1. 前期准备

### 1.1 微信公众号配置

1. 登录[微信公众平台](https://mp.weixin.qq.com/)
2. 在"设置与开发" -> "基本配置"中获取：
   - AppID
   - AppSecret
3. 在"设置与开发" -> "公众号设置" -> "功能设置"中配置：
   - JS接口安全域名（填入您的H5页面域名）

### 1.2 服务器配置

确保您的服务器可以：
- 访问微信API接口
- 处理HTTPS请求（微信JS-SDK要求HTTPS）
- 缓存access_token和jsapi_ticket（避免频繁请求）

## 2. 后端实现

### 2.1 安装依赖

```bash
# Node.js项目
npm install crypto

# 如果使用其他语言，请安装相应的加密库
```

### 2.2 实现获取配置接口

参考 `api/wxConfig.js` 文件，实现以下接口：

```
POST /api/wx/jsapi-config
Content-Type: application/json

{
  "url": "https://yourdomain.com/pages/scanWater/index"
}
```

响应格式：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "appId": "your_app_id",
    "timestamp": "1234567890",
    "nonceStr": "random_string",
    "signature": "generated_signature"
  }
}
```

### 2.3 重要注意事项

1. **缓存机制**：access_token有效期7200秒，jsapi_ticket有效期7200秒，必须缓存避免频繁请求
2. **URL处理**：传入的URL必须是完整的页面URL，不包含#及其后面的部分
3. **签名算法**：严格按照微信官方文档的签名算法实现

## 3. 前端使用

### 3.1 工具类使用

```javascript
import wxJSSDK from '@/utils/wxJSSDK.js'

// 初始化（通常在页面加载时调用）
await wxJSSDK.init()

// 扫码
try {
  const result = await wxJSSDK.scanQRCode({
    needResult: 1,
    scanType: ["qrCode", "barCode"]
  })
  console.log('扫码结果:', result)
} catch (error) {
  console.error('扫码失败:', error)
}

// 获取位置
try {
  const location = await wxJSSDK.getLocation()
  console.log('位置信息:', location)
} catch (error) {
  console.error('获取位置失败:', error)
}

// 选择图片
try {
  const images = await wxJSSDK.chooseImage({
    count: 3,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  })
  console.log('选择的图片:', images)
} catch (error) {
  console.error('选择图片失败:', error)
}
```

### 3.2 页面中使用

```vue
<template>
  <view class="scan-page">
    <button @click="handleScan">扫码</button>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import wxJSSDK from '@/utils/wxJSSDK.js'

onLoad(async () => {
  // 初始化微信JS-SDK
  await wxJSSDK.init()
})

const handleScan = async () => {
  try {
    if (wxJSSDK.isWeixinBrowser()) {
      // 微信环境使用微信扫码
      const result = await wxJSSDK.scanQRCode()
      console.log('扫码结果:', result)
    } else {
      // 非微信环境使用uni-app原生扫码
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res.result)
        }
      })
    }
  } catch (error) {
    console.error('扫码失败:', error)
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    })
  }
}
</script>
```

## 4. 调试和测试

### 4.1 开启调试模式

在开发阶段，可以在 `utils/wxJSSDK.js` 中设置 `debug: true`：

```javascript
wx.config({
  debug: true, // 开启调试模式，会弹出配置信息
  // ... 其他配置
})
```

### 4.2 常见问题排查

1. **签名错误**：
   - 检查URL是否正确（不包含#及后面部分）
   - 检查timestamp、nonceStr、signature生成是否正确
   - 检查jsapi_ticket是否有效

2. **接口调用失败**：
   - 检查JS接口安全域名是否配置正确
   - 检查是否在HTTPS环境下访问
   - 检查jsApiList是否包含所需接口

3. **初始化失败**：
   - 检查网络连接
   - 检查微信JS-SDK是否正确加载
   - 检查后端API是否正常返回

### 4.3 测试步骤

1. 在微信中打开H5页面
2. 打开微信开发者工具的控制台
3. 查看是否有错误信息
4. 测试各项功能是否正常

## 5. 生产环境部署

### 5.1 安全配置

1. 关闭调试模式：`debug: false`
2. 配置正确的JS接口安全域名
3. 确保HTTPS证书有效
4. 实现access_token和jsapi_ticket的缓存机制

### 5.2 性能优化

1. 缓存微信JS-SDK脚本
2. 实现配置信息的本地缓存
3. 优化网络请求

## 6. 支持的功能

当前工具类支持以下微信JS-SDK功能：

- ✅ 扫码（scanQRCode）
- ✅ 获取地理位置（getLocation）
- ✅ 选择图片（chooseImage）
- ⏳ 上传图片（uploadImage）- 可扩展
- ⏳ 分享功能 - 可扩展
- ⏳ 支付功能 - 可扩展

如需添加更多功能，请参考微信JS-SDK官方文档进行扩展。

## 7. 参考资料

- [微信JS-SDK官方文档](https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html)
- [uni-app官方文档](https://uniapp.dcloud.io/)
- [微信公众平台](https://mp.weixin.qq.com/)
