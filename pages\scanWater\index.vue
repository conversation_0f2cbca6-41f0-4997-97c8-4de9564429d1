<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater">
		<text class="iconfont">&#xe600;</text>
		<text class="btn-text">扫码用水</text>
	</view>
</template>

<script setup>

import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import wxJSSDK from '@/utils/wxJSSDK.js'

// 生命周期钩子
onLoad(async () => {
	// 初始化微信JS-SDK
	try {
		await wxJSSDK.init()
	} catch (error) {
		console.error('微信JS-SDK初始化失败:', error)
	}
})

// 开始扫码
const scanWater = async () => {
	console.log('开始扫码')

	try {
		let result = ''

		// #ifdef H5
		if (wxJSSDK.isWeixinBrowser()) {
			// 在微信浏览器中使用微信JS-SDK扫码
			try {
				result = await wxJSSDK.scanQRCode({
					needResult: 1,
					scanType: ["qrCode", "barCode"]
				})
			} catch (wxError) {
				console.log('微信扫码失败，尝试使用原生扫码:', wxError)
				// 微信扫码失败，降级使用uni-app原生扫码
				const res = await uniScanCode()
				result = res.result
			}
		} else {
			// 非微信环境，使用uni-app原生扫码
			const res = await uniScanCode()
			result = res.result
		}
		// #endif

		// #ifndef H5
		// 非H5环境，使用uni-app原生扫码
		const res = await uniScanCode()
		result = res.result
		// #endif

		if (result) {
			handleScanResult(result)
		}
	} catch (error) {
		console.error('扫码失败:', error)
		uni.showToast({
			title: '扫码失败',
			icon: 'none'
		})
	}
}

// uni-app原生扫码的Promise封装
const uniScanCode = () => {
	return new Promise((resolve, reject) => {
		uni.scanCode({
			success: (res) => {
				console.log('uni-app扫码成功:', res)
				resolve(res)
			},
			fail: (err) => {
				console.error('uni-app扫码失败:', err)
				reject(err)
			}
		})
	})
}

// 处理扫码结果
const handleScanResult = (result) => {
	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，可以进行下一步操作
				console.log('用户确认扫码结果')
				// 这里可以添加具体的业务逻辑，比如调用用水接口等
			}
		}
	})
}

</script>
<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}
</style>
