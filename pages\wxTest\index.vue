<template>
  <view class="wx-test-page">
    <view class="header">
      <text class="title">微信JS-SDK功能测试</text>
    </view>
    
    <view class="status-info">
      <view class="status-item">
        <text class="label">当前环境：</text>
        <text class="value">{{ currentEnv }}</text>
      </view>
      <view class="status-item">
        <text class="label">微信浏览器：</text>
        <text class="value">{{ isWeixin ? '是' : '否' }}</text>
      </view>
      <view class="status-item">
        <text class="label">JS-SDK状态：</text>
        <text class="value" :class="{ 'success': sdkReady, 'error': !sdkReady }">
          {{ sdkStatus }}
        </text>
      </view>
    </view>
    
    <view class="function-list">
      <view class="function-item" @click="testScan">
        <text class="icon">📷</text>
        <text class="name">扫码测试</text>
      </view>
      
      <view class="function-item" @click="testLocation">
        <text class="icon">📍</text>
        <text class="name">获取位置</text>
      </view>
      
      <view class="function-item" @click="testChooseImage">
        <text class="icon">🖼️</text>
        <text class="name">选择图片</text>
      </view>
      
      <view class="function-item" @click="reinitSDK">
        <text class="icon">🔄</text>
        <text class="name">重新初始化</text>
      </view>
    </view>
    
    <view class="result-area" v-if="testResult">
      <view class="result-title">测试结果：</view>
      <view class="result-content">{{ testResult }}</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import wxJSSDK from '@/utils/wxJSSDK.js'

// 响应式数据
const currentEnv = ref('')
const isWeixin = ref(false)
const sdkReady = ref(false)
const sdkStatus = ref('未初始化')
const testResult = ref('')

// 生命周期钩子
onLoad(async () => {
  // 检测当前环境
  // #ifdef H5
  currentEnv.value = 'H5'
  // #endif
  // #ifdef MP-WEIXIN
  currentEnv.value = '微信小程序'
  // #endif
  // #ifdef APP-PLUS
  currentEnv.value = 'App'
  // #endif
  
  // 检测是否在微信浏览器中
  isWeixin.value = wxJSSDK.isWeixinBrowser()
  
  // 初始化微信JS-SDK
  await initSDK()
})

// 初始化SDK
const initSDK = async () => {
  try {
    sdkStatus.value = '初始化中...'
    const result = await wxJSSDK.init()
    
    if (result) {
      sdkReady.value = true
      sdkStatus.value = '初始化成功'
    } else {
      sdkReady.value = false
      sdkStatus.value = '初始化失败'
    }
  } catch (error) {
    console.error('SDK初始化失败:', error)
    sdkReady.value = false
    sdkStatus.value = '初始化异常'
  }
}

// 重新初始化SDK
const reinitSDK = async () => {
  testResult.value = ''
  await initSDK()
}

// 测试扫码功能
const testScan = async () => {
  try {
    testResult.value = '扫码中...'
    
    if (isWeixin.value && sdkReady.value) {
      // 使用微信JS-SDK扫码
      const result = await wxJSSDK.scanQRCode({
        needResult: 1,
        scanType: ["qrCode", "barCode"]
      })
      testResult.value = `微信扫码成功：${result}`
    } else {
      // 使用uni-app原生扫码
      uni.scanCode({
        success: (res) => {
          testResult.value = `原生扫码成功：${res.result}`
        },
        fail: (err) => {
          testResult.value = `扫码失败：${JSON.stringify(err)}`
        }
      })
    }
  } catch (error) {
    console.error('扫码测试失败:', error)
    testResult.value = `扫码失败：${error.message}`
  }
}

// 测试获取位置
const testLocation = async () => {
  try {
    testResult.value = '获取位置中...'
    
    if (isWeixin.value && sdkReady.value) {
      // 使用微信JS-SDK获取位置
      const location = await wxJSSDK.getLocation()
      testResult.value = `位置获取成功：纬度${location.latitude}，经度${location.longitude}`
    } else {
      // 使用uni-app原生获取位置
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          testResult.value = `位置获取成功：纬度${res.latitude}，经度${res.longitude}`
        },
        fail: (err) => {
          testResult.value = `获取位置失败：${JSON.stringify(err)}`
        }
      })
    }
  } catch (error) {
    console.error('位置获取测试失败:', error)
    testResult.value = `获取位置失败：${error.message}`
  }
}

// 测试选择图片
const testChooseImage = async () => {
  try {
    testResult.value = '选择图片中...'
    
    if (isWeixin.value && sdkReady.value) {
      // 使用微信JS-SDK选择图片
      const images = await wxJSSDK.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })
      testResult.value = `图片选择成功：${images.length}张图片`
    } else {
      // 使用uni-app原生选择图片
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          testResult.value = `图片选择成功：${res.tempFilePaths.length}张图片`
        },
        fail: (err) => {
          testResult.value = `选择图片失败：${JSON.stringify(err)}`
        }
      })
    }
  } catch (error) {
    console.error('图片选择测试失败:', error)
    testResult.value = `选择图片失败：${error.message}`
  }
}
</script>

<style lang="scss" scoped>
.wx-test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.status-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 28rpx;
      color: #666;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      
      &.success {
        color: #07c160;
      }
      
      &.error {
        color: #fa5151;
      }
    }
  }
}

.function-list {
  .function-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #f8f8f8;
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    .icon {
      font-size: 40rpx;
      margin-right: 20rpx;
    }
    
    .name {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.result-area {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  
  .result-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 15rpx;
  }
  
  .result-content {
    font-size: 26rpx;
    color: #333;
    line-height: 1.6;
    word-break: break-all;
  }
}
</style>
