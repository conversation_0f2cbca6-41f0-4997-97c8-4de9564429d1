/**
 * 微信JS-SDK工具类
 * 用于在H5页面中集成微信JS-SDK功能
 */

class WxJSSDK {
  constructor() {
    this.isInitialized = false
    this.isWxReady = false
  }

  /**
   * 判断是否在微信浏览器中
   */
  isWeixinBrowser() {
    // #ifdef H5
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
    // #endif
    // #ifndef H5
    return false
    // #endif
  }

  /**
   * 动态加载微信JS-SDK
   */
  loadWxJSSDK() {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      if (window.wx) {
        resolve()
        return
      }
      
      const script = document.createElement('script')
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
      script.onload = () => {
        resolve()
      }
      script.onerror = () => {
        reject(new Error('微信JS-SDK加载失败'))
      }
      document.head.appendChild(script)
      // #endif
      // #ifndef H5
      resolve()
      // #endif
    })
  }

  /**
   * 从后端获取微信配置信息
   */
  async getWxConfig() {
    // #ifdef H5
    const url = window.location.href.split('#')[0] // 当前页面URL，不包含#及其后面部分
    
    try {
      // 这里需要根据您的实际后端API进行调整
      const response = await uni.request({
        url: '/api/wx/jsapi-config', // 您的后端API地址
        method: 'POST',
        data: {
          url: url
        }
      })
      
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取微信配置失败')
      }
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    }
    // #endif
    // #ifndef H5
    return {}
    // #endif
  }

  /**
   * 初始化微信JS-SDK
   */
  async init() {
    // #ifdef H5
    if (!this.isWeixinBrowser()) {
      console.log('非微信浏览器环境')
      return false
    }

    if (this.isInitialized) {
      return this.isWxReady
    }

    try {
      // 从后端获取微信JS-SDK配置信息
      const config = await this.getWxConfig()
      
      // 动态加载微信JS-SDK
      await this.loadWxJSSDK()
      
      // 配置微信JS-SDK
      wx.config({
        debug: false, // 生产环境建议设为false
        appId: config.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: [
          'scanQRCode',
          'chooseImage',
          'uploadImage',
          'getLocation',
          'openLocation'
        ]
      })
      
      return new Promise((resolve) => {
        wx.ready(() => {
          console.log('微信JS-SDK初始化成功')
          this.isInitialized = true
          this.isWxReady = true
          resolve(true)
        })
        
        wx.error((res) => {
          console.error('微信JS-SDK初始化失败:', res)
          this.isInitialized = true
          this.isWxReady = false
          resolve(false)
        })
      })
    } catch (error) {
      console.error('初始化微信JS-SDK失败:', error)
      this.isInitialized = true
      this.isWxReady = false
      return false
    }
    // #endif
    // #ifndef H5
    return false
    // #endif
  }

  /**
   * 微信扫码
   */
  scanQRCode(options = {}) {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      if (!this.isWxReady) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      wx.scanQRCode({
        needResult: options.needResult || 1, // 默认返回扫描结果
        scanType: options.scanType || ["qrCode", "barCode"], // 扫描类型
        success: function (res) {
          console.log('微信扫码成功:', res)
          resolve(res.resultStr)
        },
        fail: function (err) {
          console.error('微信扫码失败:', err)
          reject(err)
        }
      })
      // #endif
      // #ifndef H5
      reject(new Error('非H5环境不支持微信扫码'))
      // #endif
    })
  }

  /**
   * 获取地理位置
   */
  getLocation(options = {}) {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      if (!this.isWxReady) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      wx.getLocation({
        type: options.type || 'wgs84', // 默认为GPS坐标
        success: function (res) {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            speed: res.speed,
            accuracy: res.accuracy
          })
        },
        fail: function (err) {
          reject(err)
        }
      })
      // #endif
      // #ifndef H5
      reject(new Error('非H5环境不支持微信获取位置'))
      // #endif
    })
  }

  /**
   * 选择图片
   */
  chooseImage(options = {}) {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      if (!this.isWxReady) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      wx.chooseImage({
        count: options.count || 1, // 默认选择1张
        sizeType: options.sizeType || ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: options.sourceType || ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          resolve(res.localIds) // 返回选定照片的本地ID列表
        },
        fail: function (err) {
          reject(err)
        }
      })
      // #endif
      // #ifndef H5
      reject(new Error('非H5环境不支持微信选择图片'))
      // #endif
    })
  }
}

// 创建单例实例
const wxJSSDK = new WxJSSDK()

export default wxJSSDK
